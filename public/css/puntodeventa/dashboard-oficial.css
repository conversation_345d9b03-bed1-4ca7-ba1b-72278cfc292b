/* ===== DASHBOARD OFICIAL - SISTEMA DE DISEÑO CORPORATIVO PROFESIONAL ===== */

:root {
    /* Paleta de colores corporativa refinada */
    --primary-blue: #2563eb;
    --primary-blue-light: #3b82f6;
    --primary-blue-dark: #1d4ed8;
    --secondary-blue: #1e40af;

    --success-green: #059669;
    --success-green-light: #10b981;
    --warning-amber: #d97706;
    --danger-red: #dc2626;

    /* Escala de grises profesional */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --white: #ffffff;

    /* Sistema de sombras corporativo */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Sistema de bordes */
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;

    /* Espaciado consistente */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* ===== BASE Y TIPOGRAFÍA CORPORATIVA ===== */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--gray-50);
    color: var(--gray-700);
    line-height: 1.6;
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Jerarquía tipográfica profesional */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.dashboard-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-lg) 0;
    margin-bottom: var(--spacing-xl);
}

.dashboard-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.dashboard-title i {
    color: var(--primary-blue);
    font-size: 1.5rem;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-title i {
    color: var(--primary-blue);
    font-size: 1rem;
}

/* ===== SISTEMA DE CARDS CORPORATIVO ===== */
.corporate-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease-in-out;
    overflow: hidden;
}

.corporate-card:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
}

.corporate-card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-100);
    background: var(--gray-50);
}

.corporate-card-body {
    padding: var(--spacing-lg);
}

.corporate-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.corporate-card-title i {
    color: var(--primary-blue);
}

/* ===== MÉTRICAS PRINCIPALES CORPORATIVAS ===== */
.metric-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-blue);
}

.metric-card.success::before {
    background: var(--success-green);
}

.metric-card.warning::before {
    background: var(--warning-amber);
}

.metric-card.danger::before {
    background: var(--danger-red);
}

.metric-card:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    font-size: 1.25rem;
    color: var(--white);
}

.metric-icon.success {
    background: var(--success-green);
}

.metric-icon.warning {
    background: var(--warning-amber);
}

.metric-icon.primary {
    background: var(--primary-blue);
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
    line-height: 1.2;
}

.metric-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--spacing-xs);
}

.metric-description {
    font-size: 0.75rem;
    color: var(--gray-500);
    line-height: 1.4;
}

/* ===== FORMULARIOS Y CONTROLES CORPORATIVOS ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    display: block;
}

.form-control, .form-select {
    background: var(--white);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    color: var(--gray-700);
    transition: all 0.2s ease-in-out;
    width: 100%;
}

.form-control:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control:hover, .form-select:hover {
    border-color: var(--gray-400);
}

.form-control::placeholder {
    color: var(--gray-400);
}

/* Select2 personalizado */
.select2-container--bootstrap-5 .select2-selection {
    border: 1px solid var(--gray-300) !important;
    border-radius: var(--radius-md) !important;
    padding: 0.5rem !important;
    min-height: 44px !important;
    background: var(--white) !important;
}

.select2-container--bootstrap-5 .select2-selection:focus-within {
    border-color: var(--primary-blue) !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

{% if is_supervisor %}
{% extends 'supervisor_layout.html.twig' %}
{% endif %}

{% block titleHead %}{% endblock %}
{% block title %}Reporte de Productos{% endblock %}

{% block stylesheets %}
{{ parent() }}
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<!-- DataTables CSS -->
<link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
<link href="https://cdn.datatables.net/buttons/2.3.2/css/buttons.bootstrap5.min.css" rel="stylesheet" />
<!-- Dashboard Oficial CSS -->
<link href="{{ asset('css/puntodeventa/dashboard-oficial.css') }}" rel="stylesheet" />
{% endblock %}

{% block content %}
<!-- Executive Dashboard -->
<div class="executive-dashboard" data-theme="light">

  <!-- Header con controles -->
  <div style="position: relative; margin-bottom: 1rem;">
    <!-- Theme Toggle Button - Posición ajustada -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Cambiar tema" style="position: fixed; top: 80px; right: 20px; z-index: 1000;">
      <span id="theme-icon">🌙</span>
    </button>

    <!-- Botón de Cerrar Sesión - Posición prominente -->
    <div style="position: absolute; top: 0; right: 0; z-index: 999;">
      <button onclick="confirmarCerrarSesion()" style="padding: 0.75rem 1.5rem; background: var(--danger-red); color: white; border: none; border-radius: 12px; font-size: 0.875rem; font-weight: 600; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; gap: 0.5rem; box-shadow: var(--shadow-lg);" onmouseover="this.style.background='#c53030'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(220, 53, 69, 0.3)'" onmouseout="this.style.background='var(--danger-red)'; this.style.transform='translateY(0)'; this.style.boxShadow='var(--shadow-lg)'">
        <i class="fas fa-sign-out-alt"></i>
        Cerrar Sesión
      </button>
    </div>
  </div>

  <!-- Hero Section - Métricas Principales -->
  <section class="hero-metrics">
    <div class="hero-header">
      <div>
        <h1 class="hero-title">
          <i class="fas fa-tachometer-alt" style="color: var(--primary-blue); margin-right: 0.5rem;"></i>
          {% if is_supervisor %}Dashboard Supervisor{% else %}Dashboard Administrador{% endif %}
        </h1>
        <p class="hero-subtitle">
          <i class="fas fa-building" style="margin-right: 0.25rem;"></i>
          Optimo Opticas - Resumen Ejecutivo
        </p>
      </div>
      <div style="text-align: right; color: var(--gray-600); font-size: 0.875rem; margin-right: 180px;">
        <div><i class="fas fa-calendar"></i> <span id="current-date"></span></div>
        <div><i class="fas fa-clock"></i> <span id="current-time"></span></div>
      </div>
    </div>

    <!-- Métricas Principales -->
    <div class="metrics-grid">
      <div class="metric-card success">
        <div class="metric-header">
          <div class="metric-icon success">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="metric-trend positive" id="ventas-trend">
            <i class="fas fa-arrow-up"></i> +12.5%
          </div>
        </div>
        <div class="metric-value" id="ventas-total">$0.00</div>
        <div class="metric-label">Ventas del Día</div>
        <div class="metric-description">Total de ventas registradas hoy</div>
      </div>

      <div class="metric-card primary">
        <div class="metric-header">
          <div class="metric-icon primary">
            <i class="fas fa-money-bill-wave"></i>
          </div>
          <div class="metric-trend positive" id="pagos-trend">
            <i class="fas fa-arrow-up"></i> +8.3%
          </div>
        </div>
        <div class="metric-value" id="pagos-total">$0.00</div>
        <div class="metric-label">Pagos Recibidos</div>
        <div class="metric-description">Total de pagos cobrados hoy</div>
      </div>

      <div class="metric-card warning">
        <div class="metric-header">
          <div class="metric-icon warning">
            <i class="fas fa-clock"></i>
          </div>
          <div class="metric-trend neutral" id="deuda-trend">
            <i class="fas fa-minus"></i> 0%
          </div>
        </div>
        <div class="metric-value" id="por-cobrar-total">$0.00</div>
        <div class="metric-label">Por Cobrar</div>
        <div class="metric-description">Pendiente de cobro</div>
      </div>
    </div>
  </section>

  <!-- Quick Actions Section -->
  <section class="quick-actions" style="margin-bottom: 2rem;">
    <div style="background: var(--white); border-radius: 12px; padding: 1.5rem; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200);">
      <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
        <i class="fas fa-sliders-h" style="color: var(--primary-blue);"></i>
        Acciones Rápidas
      </h3>

      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
        <div>
          <label for="fecha-hoy" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-calendar"></i> Fecha:
          </label>
          <input type="date" id="fecha-hoy" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900);">
        </div>

        <div>
          <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-store"></i> Sucursales:
          </label>
          <div id="sucursales-summary" style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--gray-50); color: var(--gray-700); cursor: pointer;" onclick="toggleSucursalesPanel()">
            <span id="sucursales-count">Cargando...</span>
            <i class="fas fa-chevron-down" style="float: right; margin-top: 0.125rem;"></i>
          </div>
        </div>

        <div>
          <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-building"></i> Empresa:
          </label>
          {% if not is_hardcoded %}
            <select name="empresa" id="idempresa" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900);" onchange="cargaDefiltros();">
              <option value="-1">Seleccione una empresa</option>
              {% for empresa in empresas %}
                <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
              {% endfor %}
            </select>
          {% else %}
            <input type="hidden" id="idempresa" value="{{ empresas[0].idempresa }}">
            <div style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--gray-50); color: var(--gray-700);">
              <i class="fas fa-lock" style="margin-right: 0.5rem;"></i>
              {{ empresas[0].nombre }} (BIMBO)
            </div>
          {% endif %}
        </div>

        <div>
          <button onclick="buscarIngresosDiarios()" style="width: 100%; padding: 0.75rem 1rem; background: var(--primary-blue); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; gap: 0.5rem;" onmouseover="this.style.background='var(--primary-blue-dark)'" onmouseout="this.style.background='var(--primary-blue)'">
            <i class="fas fa-sync-alt"></i>
            Actualizar
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Sucursales Panel (Collapsible) -->
  <div id="sucursales-panel" style="display: none; margin-bottom: 2rem;">
    <div style="background: var(--white); border-radius: 12px; padding: 1.5rem; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200);">
      <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0;">
        <i class="fas fa-store"></i> Seleccionar Sucursales
      </h4>
      <div id="sucursales"></div>
    </div>
  </div>

  <!-- Hidden inputs for compatibility -->
  <input type="hidden" id="tipo-venta" value="BIMBO">

  <!-- Detailed Analysis Section (Open by default) -->
  <section class="detailed-analysis">
    <details class="analysis-section" open style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-chart-pie" style="color: var(--primary-blue);"></i>
        📊 Análisis de Ingresos Diarios
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
        <!-- Charts Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem; margin-top: 1.5rem;">

          <!-- Ingresos por Sucursal -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-store" style="color: var(--success-green);"></i>
              Ingresos por Sucursal
            </h4>
            <div id="graficaSucursal" style="height: 300px; min-height: 300px;"></div>
          </div>

          <!-- Tipos de Pago -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-credit-card" style="color: var(--primary-blue);"></i>
              Tipos de Pago
            </h4>
            <div id="graficaTipoPago" style="height: 300px; min-height: 300px;"></div>
          </div>

          <!-- Deuda por Sucursal -->
          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200); grid-column: 1 / -1;">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-exclamation-triangle" style="color: var(--warning-amber);"></i>
              Deuda por Sucursal
            </h4>
            <div id="deudaTotal" style="height: 300px; min-height: 300px; display: flex; align-items: center; justify-content: center; color: var(--gray-500);">
              <div style="text-align: center;">
                <i class="fas fa-spinner fa-spin fa-2x" style="margin-bottom: 1rem;"></i>
                <p>Cargando datos...</p>
              </div>
            </div>
          </div>

        </div>

        <!-- DataTable Section -->
        <div style="margin-top: 2rem;">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-table" style="color: var(--info);"></i>
            Detalle de Ingresos por Sucursal
          </h4>

          <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
            <table id="tablaIngresosDiarios" class="table table-striped table-hover" style="width: 100%; margin: 0;">
              <tbody>
                <!-- DataTables manejará el contenido -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </details>
  </section>

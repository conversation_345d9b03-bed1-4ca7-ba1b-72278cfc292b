<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\ORM\EntityManagerInterface;
use App\Entity\Empresa;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\Clase;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;

class OficialController extends AbstractController
{
    /**
     * @Route("/reporte/dashboard", name="app_oficial")
     * @Security("is_granted('ROLE_SUPERVISOR') or is_granted('ROLE_SUPER_ADMIN')")
     */
    public function index(EntityManagerInterface $em): Response
    {
        // Verificar si el usuario es supervisor para mostrar vista sin menú
        $user = $this->getUser();
        $isSupervisor = in_array('ROLE_SUPERVISOR', $user->getRoles());
        
        if ($isSupervisor) {
            // Para supervisores, buscar el tipo de venta 1799 (BIMBO) y su empresa asociada
            $tipoVenta = $em->getRepository('App\Entity\Tipoventa')->find(1799);
            
            if (!$tipoVenta) {
                throw $this->createNotFoundException('Tipo de venta 1799 (BIMBO) no encontrado');
            }
            
            $empresa = $tipoVenta->getEmpresaIdempresa();
            
            if (!$empresa) {
                throw $this->createNotFoundException('Empresa asociada al tipo de venta 1799 (BIMBO) no encontrada');
            }
            
            // Solo pasar la empresa asociada al tipo de venta 1799 (BIMBO)
            $empresas = [$empresa];
            $isHardcoded = true;
        } else {
            // Para administradores, mostrar todas las empresas
            $empresas = $em->getRepository(Empresa::class)->findAll();
            $isHardcoded = false;
        }

        return $this->render('oficial/index.html.twig', [
            'controller_name' => 'OficialController',
            'empresas' => $empresas,
            'is_supervisor' => $isSupervisor,
            'is_hardcoded' => $isHardcoded,
        ]);
    }

    /**
     * @Route("/ajax/clases", name="almacen-obtener-clase")
     */
    public function obtenerClases(Request $request, EntityManagerInterface $em): Response
    {
        $idempresa = $request->query->get('idempresa');
        $clases = $em->getRepository(Clase::class)->findBy(['empresaIdempresa' => $idempresa]);
        return $this->render('almacen/clases.html.twig', [
            'clases' => $clases,
        ]);
    }
}
